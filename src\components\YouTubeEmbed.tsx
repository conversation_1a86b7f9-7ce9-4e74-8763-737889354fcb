'use client';

import { useState } from 'react';
import { Play } from 'lucide-react';
import Image from 'next/image';

interface YouTubeEmbedProps {
  videoId: string;
  title: string;
  className?: string;
  autoplay?: boolean;
  showPrivacyNotice?: boolean;
}

export default function YouTubeEmbed({
  videoId,
  title,
  className = '',
  autoplay = false,
  showPrivacyNotice = true
}: YouTubeEmbedProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [thumbnailError, setThumbnailError] = useState(false);

  // Use privacy-enhanced mode by default
  const embedUrl = `https://www.youtube-nocookie.com/embed/${videoId}?${new URLSearchParams({
    rel: '0', // Don't show related videos from other channels
    modestbranding: '1', // Minimal YouTube branding
    playsinline: '1', // Play inline on mobile
    enablejsapi: '1', // Enable JavaScript API
    origin: typeof window !== 'undefined' ? window.location.origin : '',
    ...(autoplay && { autoplay: '1' })
  }).toString()}`;

  // Try multiple thumbnail qualities
  const thumbnailUrl = thumbnailError
    ? `https://i3.ytimg.com/vi/${videoId}/hqdefault.jpg`
    : `https://i3.ytimg.com/vi/${videoId}/maxresdefault.jpg`;

  const handleLoadVideo = () => {
    setIsLoaded(true);
  };

  const handleThumbnailError = () => {
    if (!thumbnailError) {
      setThumbnailError(true); // Try lower quality thumbnail
    } else {
      setHasError(true); // If both thumbnails fail, show error
    }
  };

  const handleIframeError = () => {
    setHasError(true);
  };

  if (hasError) {
    return (
      <div className={`relative bg-gray-100 rounded-lg overflow-hidden ${className}`}>
        <div className="aspect-video flex items-center justify-center">
          <div className="text-center p-8">
            <p className="text-gray-600 mb-4">Unable to load video</p>
            <a 
              href={`https://www.youtube.com/watch?v=${videoId}`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Watch on YouTube
            </a>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative bg-gray-100 rounded-lg overflow-hidden shadow-lg ${className}`}>
      {!isLoaded ? (
        // Thumbnail with play button (lazy loading)
        <div className="relative aspect-video cursor-pointer group" onClick={handleLoadVideo}>
          <Image
            src={thumbnailUrl}
            alt={title}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            loading="lazy"
            onError={handleThumbnailError}
            fill
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center group-hover:bg-opacity-40 transition-all duration-300">
            <div className="bg-red-600 rounded-full p-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <Play className="w-8 h-8 text-white fill-current ml-1" />
            </div>
          </div>
          <div className="absolute bottom-4 left-4 right-4">
            <h3 className="text-white font-semibold text-lg drop-shadow-lg overflow-hidden">
              {title}
            </h3>
          </div>
        </div>
      ) : (
        // Actual YouTube iframe
        <div className="aspect-video">
          <iframe
            src={embedUrl}
            title={title}
            className="w-full h-full border-0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowFullScreen
            loading="lazy"
            onError={handleIframeError}
          />
        </div>
      )}
      
      {showPrivacyNotice && !isLoaded && (
        <div className="absolute top-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
          Privacy-enhanced mode
        </div>
      )}
    </div>
  );
}
